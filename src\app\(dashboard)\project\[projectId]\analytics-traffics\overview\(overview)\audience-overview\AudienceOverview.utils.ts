import type {
  AudienceOverviewApiResponse,
  ChartResponse,
} from "./AudienceOverview.types";

/**
 * Formats a number to a readable string with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Formats percentage to display with % symbol
 */
export const formatPercentage = (rate: number): string => {
  return (rate * 100).toFixed(1) + "%";
};

/**
 * Formats time in seconds to readable format
 */
export const formatTime = (seconds: number): string => {
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${Math.floor(seconds)}s`;
};

/**
 * Formats date from YYYYMMDD to readable format
 * Uses month abbreviation and day for better readability
 */
export const formatDate = (dateStr: string): string => {
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);
  const date = new Date(`${year}-${month}-${day}`);

  // Use format: Jul 16
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between current and previous period
 */
export const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return "+0%";
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

/**
 * Creates chart data points with optional comparison values
 */
const createChartDataPoints = (
  primaryMetrics: any[],
  comparisonMetrics: any[] | undefined,
  valueKey: string
) => {
  return primaryMetrics.map((metric, index) => {
    const comparisonMetric = comparisonMetrics?.[index];
    return {
      name: formatDate(metric.date),
      value: metric[valueKey],
      ...(comparisonMetric && { comparisonValue: comparisonMetric[valueKey] }),
    };
  });
};

/**
 * Transforms the audience overview API response into chart data format
 * Supports dual period comparison when comparisonResponse is provided
 */
export const transformAudienceOverviewData = (
  primaryResponse: AudienceOverviewApiResponse,
  comparisonResponse?: AudienceOverviewApiResponse | null
): ChartResponse[] => {
  const { daily_metrics: primaryMetrics, totals: primaryTotals } =
    primaryResponse.data;
  const comparisonTotals = comparisonResponse?.data.totals;
  const comparisonMetrics = comparisonResponse?.data.daily_metrics;
  const hasComparison = !!comparisonResponse;

  // Create chart data for each metric
  const charts: ChartResponse[] = [
    {
      id: 1,
      title: "Total Users",
      bigNumber: formatNumber(primaryTotals.total_users),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.total_users,
            comparisonTotals.total_users
          )
        : "+0%",
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "total_users"
      ),
      hasComparison,
    },
    {
      id: 2,
      title: "New Users",
      bigNumber: formatNumber(primaryTotals.new_users),
      smallNumber: comparisonTotals
        ? calculateGrowth(primaryTotals.new_users, comparisonTotals.new_users)
        : "+0%",
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "new_users"
      ),
      hasComparison,
    },
    {
      id: 3,
      title: "Returning Users",
      bigNumber: formatNumber(primaryTotals.returning_users),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.returning_users,
            comparisonTotals.returning_users
          )
        : formatPercentage(primaryTotals.returning_users_rate / 100),
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "returning_users"
      ),
      hasComparison,
    },
    {
      id: 4,
      title: "Page Views",
      bigNumber: formatNumber(primaryTotals.views),
      smallNumber: comparisonTotals
        ? calculateGrowth(primaryTotals.views, comparisonTotals.views)
        : "+0%",
      data: createChartDataPoints(primaryMetrics, comparisonMetrics, "views"),
      hasComparison,
    },
    {
      id: 5,
      title: "Sessions",
      bigNumber: formatNumber(primaryTotals.sessions),
      smallNumber: comparisonTotals
        ? calculateGrowth(primaryTotals.sessions, comparisonTotals.sessions)
        : "+0%",
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "sessions"
      ),
      hasComparison,
    },
    {
      id: 6,
      title: "Engaged Sessions",
      bigNumber: formatNumber(primaryTotals.engaged_sessions),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.engaged_sessions,
            comparisonTotals.engaged_sessions
          )
        : formatPercentage(primaryTotals.engagement_rate),
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "engaged_sessions"
      ),
      hasComparison,
    },
    {
      id: 7,
      title: "Avg. Engagement Time",
      bigNumber: formatTime(primaryTotals.avg_engagement_time),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.avg_engagement_time,
            comparisonTotals.avg_engagement_time
          )
        : "+0%",
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "avg_engagement_time"
      ),
      hasComparison,
    },
    {
      id: 9,
      title: "Active Users",
      bigNumber: formatNumber(primaryTotals.active_users),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.active_users,
            comparisonTotals.active_users
          )
        : formatPercentage(primaryTotals.active_users_rate / 100),
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "active_users"
      ),
      hasComparison,
    },
    {
      id: 8,
      title: "Event Count",
      bigNumber: formatNumber(primaryTotals.event_count),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.event_count,
            comparisonTotals.event_count
          )
        : "+0%",
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "event_count"
      ),
      hasComparison,
    },
    {
      id: 10,
      title: "Engagement Rate",
      bigNumber: formatPercentage(primaryTotals.engagement_rate),
      smallNumber: comparisonTotals
        ? calculateGrowth(
            primaryTotals.engagement_rate,
            comparisonTotals.engagement_rate
          )
        : "+0%",
      data: primaryMetrics.map((metric, index) => {
        const comparisonMetric = comparisonMetrics?.[index];
        return {
          name: formatDate(metric.date),
          value: metric.engagement_rate * 100, // Convert to percentage for chart
          ...(comparisonMetric && {
            comparisonValue: comparisonMetric.engagement_rate * 100,
          }),
        };
      }),
      hasComparison,
    },
  ];

  return charts;
};
